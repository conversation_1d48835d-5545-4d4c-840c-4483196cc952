'use client';

// Hybrid embedding function - tries client-side first, falls back to server-side, then mathematical
export async function generateQAEmbedding(question: string, answer: string = ''): Promise<number[]> {
  // First, try client-side embedding generation
  try {
    console.log('🧠 Attempting client-side embedding generation...');
    return await generateClientSideEmbedding(question, answer);
  } catch (clientError) {
    console.warn('⚠️ Client-side embedding failed, trying server-side:', clientError);
    try {
      console.log('🔄 Attempting server-side embedding as fallback...');
      return await generateServerSideEmbedding(question, answer);
    } catch (serverError) {
      console.warn('⚠️ Server-side embedding failed, using mathematical fallback:', serverError);
      console.log('🔢 Using mathematical fallback embedding generation...');
      const combinedText = answer ? `Question: ${question}\nAnswer: ${answer}` : `Query: ${question}`;
      const result = generateMathematicalEmbedding(combinedText);
      console.log('✅ Mathematical fallback embedding completed');
      return result;
    }
  }
}

// Specialized function for search queries (only question, no answer)
export async function generateSearchEmbedding(query: string): Promise<number[]> {
  // First, try client-side embedding generation
  try {
    console.log('🔍 Attempting client-side search embedding generation...');
    return await generateClientSideSearchEmbedding(query);
  } catch (clientError) {
    console.warn('⚠️ Client-side search embedding failed, trying server-side:', clientError);
    try {
      console.log('🔄 Attempting server-side search embedding as fallback...');
      return await generateServerSideSearchEmbedding(query);
    } catch (serverError) {
      console.warn('⚠️ Server-side search embedding failed, using mathematical fallback:', serverError);
      console.log('🔢 Using mathematical fallback for search embedding...');
      const result = generateMathematicalEmbedding(`Query: ${query}`);
      console.log('✅ Mathematical fallback search embedding completed');
      return result;
    }
  }
}

// Client-side embedding generation with improved error handling
async function generateClientSideEmbedding(question: string, answer: string): Promise<number[]> {
  try {
    console.log('🧠 Starting client-side embedding generation...');

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Client-side embedding only works in browser environment');
    }

    // Dynamic import to avoid SSR issues
    console.log('📦 Importing @xenova/transformers...');
    const { pipeline } = await import('@xenova/transformers');
    console.log('✅ @xenova/transformers imported successfully');

    console.log('🧠 Initializing embedding model...');

    // Try different device configurations with fallbacks
    let embeddingPipeline;
    const deviceOptions = [
      { device: 'cpu', dtype: 'fp32' },
      { device: 'webgpu', dtype: 'fp32' },
      { dtype: 'fp32' }, // Let the library choose
    ];

    for (let i = 0; i < deviceOptions.length; i++) {
      try {
        console.log(`🔄 Trying device configuration ${i + 1}/${deviceOptions.length}:`, deviceOptions[i]);
        embeddingPipeline = await pipeline(
          'feature-extraction',
          'Xenova/all-MiniLM-L6-v2',
          deviceOptions[i]
        );
        console.log(`✅ Embedding pipeline initialized with configuration:`, deviceOptions[i]);
        break;
      } catch (deviceError) {
        console.warn(`⚠️ Device configuration ${i + 1} failed:`, deviceError);
        if (i === deviceOptions.length - 1) {
          throw new Error(`All device configurations failed. Last error: ${deviceError.message}`);
        }
      }
    }

    // Combine question and answer with a separator for better semantic representation
    const combinedText = `Question: ${question}\nAnswer: ${answer}`;
    
    console.log('🔄 Generating embedding for text:', combinedText.substring(0, 100) + '...');

    // Generate embedding
    console.log('🔄 Running embedding model on text...');
    const result = await embeddingPipeline(combinedText, {
      pooling: 'mean',
      normalize: true,
    });
    console.log('✅ Embedding model completed');

    // Extract the embedding vector
    console.log('📊 Extracting embedding vector...');
    const embedding = Array.from(result.data) as number[];
    console.log('✅ Generated embedding with dimensions:', embedding.length);

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }

    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return embedding;
  } catch (error) {
    console.error('❌ Failed to generate embedding:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        throw new Error('Failed to download embedding model. Check your internet connection.');
      } else if (error.message.includes('memory') || error.message.includes('Memory')) {
        throw new Error('Insufficient memory to load embedding model. Try closing other browser tabs.');
      } else if (error.message.includes('WebGPU')) {
        throw new Error('WebGPU not supported. The model will use CPU instead.');
      } else {
        throw new Error(`Embedding generation failed: ${error.message}`);
      }
    }

    throw new Error('Failed to generate embedding: Unknown error');
  }
}

// Client-side search embedding generation (for queries without answers)
async function generateClientSideSearchEmbedding(query: string): Promise<number[]> {
  try {
    console.log('🔍 Starting client-side search embedding generation...');

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      throw new Error('Client-side search embedding only works in browser environment');
    }

    // Dynamic import to avoid SSR issues
    console.log('📦 Importing @xenova/transformers...');
    const { pipeline } = await import('@xenova/transformers');
    console.log('✅ @xenova/transformers imported successfully');

    console.log('🧠 Initializing embedding model...');

    // Try different device configurations with fallbacks
    let embeddingPipeline;
    const deviceOptions = [
      { device: 'cpu', dtype: 'fp32' },
      { device: 'webgpu', dtype: 'fp32' },
      { dtype: 'fp32' }, // Let the library choose
    ];

    for (let i = 0; i < deviceOptions.length; i++) {
      try {
        console.log(`🔄 Trying device configuration ${i + 1}/${deviceOptions.length}:`, deviceOptions[i]);
        embeddingPipeline = await pipeline(
          'feature-extraction',
          'Xenova/all-MiniLM-L6-v2',
          deviceOptions[i]
        );
        console.log(`✅ Search embedding pipeline initialized with configuration:`, deviceOptions[i]);
        break;
      } catch (deviceError) {
        console.warn(`⚠️ Device configuration ${i + 1} failed:`, deviceError);
        if (i === deviceOptions.length - 1) {
          throw new Error(`All device configurations failed. Last error: ${deviceError.message}`);
        }
      }
    }

    // For search queries, we just use the query text directly
    const searchText = `Query: ${query}`;

    console.log('🔄 Processing search query with embedding model...');

    // Generate embedding
    const result = await embeddingPipeline(searchText, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];

    console.log('✅ Client-side search embedding generated:', {
      dimensions: embedding.length,
      query_length: query.length,
    });

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }

    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return embedding;

  } catch (error) {
    console.error('❌ Client-side search embedding generation failed:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('CORS') || error.message.includes('fetch')) {
        throw new Error('Cannot load embedding model from CDN. Check your internet connection.');
      } else if (error.message.includes('memory') || error.message.includes('Memory')) {
        throw new Error('Insufficient memory to load embedding model. Try closing other browser tabs.');
      } else if (error.message.includes('WebGPU')) {
        throw new Error('WebGPU not supported. The model will use CPU instead.');
      } else {
        throw new Error(`Search embedding generation failed: ${error.message}`);
      }
    }

    throw new Error('Failed to generate search embedding: Unknown error');
  }
}

// Server-side embedding generation fallback
async function generateServerSideEmbedding(question: string, answer: string): Promise<number[]> {
  try {
    console.log('🔄 Generating embedding server-side...');

    const response = await fetch('/api/generate-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ question, answer }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Server responded with ${response.status}`);
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || 'Server-side embedding generation failed');
    }

    console.log('✅ Server-side embedding generated:', {
      dimensions: result.dimensions,
      method: result.method
    });

    return result.embedding;
  } catch (error) {
    console.error('❌ Server-side embedding generation failed:', error);
    throw new Error(`Server-side embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Server-side search embedding generation fallback (for queries without answers)
async function generateServerSideSearchEmbedding(query: string): Promise<number[]> {
  try {
    console.log('🔍 Generating search embedding server-side...');

    const response = await fetch('/api/generate-search-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Server responded with ${response.status}`);
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || 'Server-side search embedding generation failed');
    }

    console.log('✅ Server-side search embedding generated:', {
      dimensions: result.embedding.length,
      query_length: query.length,
    });

    return result.embedding;
  } catch (error) {
    console.error('❌ Server-side search embedding generation failed:', error);
    throw new Error(`Server-side search embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Sync Q&A pair with Qdrant
 */
export async function syncToQdrant(data: {
  question: string;
  answer: string;
  vector: number[];
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Syncing to Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      vectorDimensions: data.vector.length,
    });

    const response = await fetch('/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to sync with Qdrant');
    }

    console.log('✅ Successfully synced to Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync to Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete Q&A pair from Qdrant
 */
export async function deleteFromQdrant(data: {
  question: string;
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting from Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      question: data.question.substring(0, 50) + '...',
    });

    const response = await fetch('/api/qdrant-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      console.warn('⚠️ Qdrant deletion failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to delete from Qdrant'
      };
    }

    console.log('✅ Successfully deleted from Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to delete from Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Mathematical fallback embedding generation
 * Creates a deterministic 384-dimensional vector based on text content
 * This is a simple fallback when AI models are not available
 */
function generateMathematicalEmbedding(text: string): number[] {
  console.log('🔢 Generating mathematical fallback embedding...');

  const dimensions = 384; // Match all-MiniLM-L6-v2 dimensions
  const embedding = new Array(dimensions);

  // Normalize text
  const normalizedText = text.toLowerCase().trim();

  // Create a simple hash-based embedding
  let hash = 0;
  for (let i = 0; i < normalizedText.length; i++) {
    const char = normalizedText.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use the hash as a seed for pseudo-random number generation
  let seed = Math.abs(hash);

  // Simple linear congruential generator for reproducible "randomness"
  const lcg = (seed: number) => {
    seed = (seed * 1664525 + 1013904223) % Math.pow(2, 32);
    return seed / Math.pow(2, 32);
  };

  // Generate embedding values
  for (let i = 0; i < dimensions; i++) {
    seed = lcg(seed) * Math.pow(2, 32);
    // Create values between -1 and 1, with some text-based variation
    const charInfluence = i < normalizedText.length ?
      (normalizedText.charCodeAt(i % normalizedText.length) / 127.5) - 1 : 0;
    embedding[i] = (lcg(seed) * 2 - 1) * 0.7 + charInfluence * 0.3;
  }

  // Normalize the vector (L2 normalization)
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  if (magnitude > 0) {
    for (let i = 0; i < dimensions; i++) {
      embedding[i] = embedding[i] / magnitude;
    }
  }

  console.log('✅ Mathematical fallback embedding generated:', {
    dimensions: embedding.length,
    text_length: text.length,
    magnitude: Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0)).toFixed(4),
    sample_values: embedding.slice(0, 5).map(v => v.toFixed(4))
  });

  return embedding;
}
