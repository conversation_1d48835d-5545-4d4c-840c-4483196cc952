'use client';

// Hybrid embedding function - tries client-side first, falls back to server-side
export async function generateQAEmbedding(question: string, answer: string): Promise<number[]> {
  // First, try client-side embedding generation
  try {
    console.log('🧠 Attempting client-side embedding generation...');
    return await generateClientSideEmbedding(question, answer);
  } catch (clientError) {
    console.warn('⚠️ Client-side embedding failed, falling back to server-side:', clientError);
    return await generateServer<PERSON>ideEmbedding(question, answer);
  }
}

// Client-side embedding generation
async function generateClientSideEmbedding(question: string, answer: string): Promise<number[]> {
  try {
    console.log('🧠 Starting client-side embedding generation...');

    // Dynamic import to avoid SSR issues
    console.log('📦 Importing @xenova/transformers...');
    const { pipeline } = await import('@xenova/transformers');
    console.log('✅ @xenova/transformers imported successfully');

    console.log('🧠 Initializing embedding model...');

    // Try with CPU first for better compatibility
    let embeddingPipeline;
    try {
      embeddingPipeline = await pipeline(
        'feature-extraction',
        'Xenova/all-MiniLM-L6-v2',
        {
          device: 'cpu', // Use CPU for better compatibility
          dtype: 'fp32',
        }
      );
      console.log('✅ Embedding pipeline initialized with CPU');
    } catch (cpuError) {
      console.warn('⚠️ CPU initialization failed, trying WebGPU:', cpuError);
      // Fallback to WebGPU if CPU fails
      embeddingPipeline = await pipeline(
        'feature-extraction',
        'Xenova/all-MiniLM-L6-v2',
        {
          device: 'webgpu',
          dtype: 'fp32',
        }
      );
      console.log('✅ Embedding pipeline initialized with WebGPU');
    }

    // Combine question and answer with a separator for better semantic representation
    const combinedText = `Question: ${question}\nAnswer: ${answer}`;
    
    console.log('🔄 Generating embedding for text:', combinedText.substring(0, 100) + '...');

    // Generate embedding
    console.log('🔄 Running embedding model on text...');
    const result = await embeddingPipeline(combinedText, {
      pooling: 'mean',
      normalize: true,
    });
    console.log('✅ Embedding model completed');

    // Extract the embedding vector
    console.log('📊 Extracting embedding vector...');
    const embedding = Array.from(result.data) as number[];
    console.log('✅ Generated embedding with dimensions:', embedding.length);

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }

    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return embedding;
  } catch (error) {
    console.error('❌ Failed to generate embedding:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        throw new Error('Failed to download embedding model. Check your internet connection.');
      } else if (error.message.includes('memory') || error.message.includes('Memory')) {
        throw new Error('Insufficient memory to load embedding model. Try closing other browser tabs.');
      } else if (error.message.includes('WebGPU')) {
        throw new Error('WebGPU not supported. The model will use CPU instead.');
      } else {
        throw new Error(`Embedding generation failed: ${error.message}`);
      }
    }

    throw new Error('Failed to generate embedding: Unknown error');
  }
}

// Server-side embedding generation fallback
async function generateServerSideEmbedding(question: string, answer: string): Promise<number[]> {
  try {
    console.log('🔄 Generating embedding server-side...');

    const response = await fetch('/api/generate-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ question, answer }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Server responded with ${response.status}`);
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || 'Server-side embedding generation failed');
    }

    console.log('✅ Server-side embedding generated:', {
      dimensions: result.dimensions,
      method: result.method
    });

    return result.embedding;
  } catch (error) {
    console.error('❌ Server-side embedding generation failed:', error);
    throw new Error(`Server-side embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Sync Q&A pair with Qdrant
 */
export async function syncToQdrant(data: {
  question: string;
  answer: string;
  vector: number[];
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Syncing to Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      vectorDimensions: data.vector.length,
    });

    const response = await fetch('/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to sync with Qdrant');
    }

    console.log('✅ Successfully synced to Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync to Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete Q&A pair from Qdrant
 */
export async function deleteFromQdrant(data: {
  question: string;
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting from Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      question: data.question.substring(0, 50) + '...',
    });

    const response = await fetch('/api/qdrant-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      console.warn('⚠️ Qdrant deletion failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to delete from Qdrant'
      };
    }

    console.log('✅ Successfully deleted from Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to delete from Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
