export const APP_CONFIG = {
  name: 'odude-chat',
  description: 'Multi-user Q&A Collections SaaS',
  theme: {
    primaryColor: 'blue',
    defaultColorScheme: 'dark' as const,
  },
  routes: {
    home: '/',
    dashboard: '/dashboard',
    login: '/auth/signin',
    collections: '/collections',
  },
  rag: {
    // Supported language tones for RAG chatbot
    supportedLanguages: [
      { code: 'hi-en', name: 'Hindi-English', description: 'Mixed Hindi and English responses' },
      { code: 'ne-en', name: 'Nepali-English', description: 'Mixed Nepali and English responses' },
      { code: 'en', name: 'English', description: 'Pure English responses' },
    ],
    // Default settings
    defaultLanguage: 'en',
    maxSearchResults: 5,
    similarityThreshold: 0.7,
    // Gemini model settings
    gemini: {
      model: 'gemini-1.5-flash',
      maxTokens: 1000,
      temperature: 0.7,
    },
  },
} as const;