import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    const body = await request.json();
    const { question, answer } = body;

    // Validate input
    if (!question || !answer) {
      return NextResponse.json(
        { error: 'Both question and answer are required' },
        { status: 400 }
      );
    }

    console.log('🧠 Generating embedding server-side for Q&A pair...');

    // Import transformers library
    const { pipeline } = await import('@xenova/transformers');
    
    // Initialize embedding pipeline
    const embeddingPipeline = await pipeline(
      'feature-extraction',
      'Xenova/all-MiniLM-L6-v2',
      {
        device: 'cpu', // Use CPU on server
        dtype: 'fp32',
      }
    );

    // Combine question and answer
    const combinedText = `Question: ${question}\nAnswer: ${answer}`;
    
    console.log('🔄 Processing text with embedding model...');
    
    // Generate embedding
    const result = await embeddingPipeline(combinedText, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];
    
    console.log('✅ Server-side embedding generated:', {
      dimensions: embedding.length,
      question_length: question.length,
      answer_length: answer.length,
      user_id: user.id
    });

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }
    
    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'server-side',
      model: 'Xenova/all-MiniLM-L6-v2',
      text_length: combinedText.length,
    });

  } catch (error) {
    console.error('❌ Server-side embedding generation failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate embedding',
        details: error instanceof Error ? error.message : 'Unknown error',
        method: 'server-side'
      },
      { status: 500 }
    );
  }
}
