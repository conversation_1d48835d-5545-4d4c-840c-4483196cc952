import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { generateQAEmbedding, generateSearchEmbedding } from '@/lib/embedding-client';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    console.log('🧪 Starting complete RAG workflow test for user:', user.id);

    const testResults = {
      steps: [] as string[],
      embedding_generation: null as any,
      qa_creation: null as any,
      qdrant_sync: null as any,
      search_embedding: null as any,
      rag_search: null as any,
      rag_chat: null as any,
      cleanup: null as any,
      overall_success: false,
      performance: {} as any
    };

    const startTime = Date.now();

    // Test data
    const testData = {
      question: "What is the quality of the material?",
      answer: "The material is nylon, not cotton. The back side is transparent and the front is double layer.",
      collection_id: `test-collection-${Date.now()}`,
      user_id: user.id
    };

    try {
      // Step 1: Test embedding generation
      console.log('🔄 Step 1: Testing embedding generation...');
      const embeddingStart = Date.now();
      
      const embedding = await generateQAEmbedding(testData.question, testData.answer);
      
      const embeddingTime = Date.now() - embeddingStart;
      testResults.embedding_generation = {
        success: true,
        dimensions: embedding.length,
        magnitude: Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0)),
        sample_values: embedding.slice(0, 5).map(v => v.toFixed(4)),
        generation_time_ms: embeddingTime,
        is_zero_vector: embedding.every(v => v === 0),
        has_invalid_values: embedding.some(v => !isFinite(v))
      };
      testResults.steps.push('✅ Embedding generation successful');
      console.log('✅ Step 1 completed: Embedding generated');

      // Step 2: Create temporary collection for testing
      console.log('🔄 Step 2: Creating test collection...');
      
      const { data: collection, error: collectionError } = await supabase
        .from('collections')
        .insert({
          name: `Test Collection ${Date.now()}`,
          description: 'Temporary collection for RAG workflow testing',
          user_id: user.id,
        })
        .select()
        .single();

      if (collectionError) {
        throw new Error(`Failed to create test collection: ${collectionError.message}`);
      }

      testData.collection_id = collection.id;
      testResults.steps.push('✅ Test collection created');
      console.log('✅ Step 2 completed: Test collection created');

      // Step 3: Create Q&A pair
      console.log('🔄 Step 3: Creating Q&A pair...');
      
      const { data: qaPair, error: qaError } = await supabase
        .from('qa_pairs')
        .insert({
          question: testData.question,
          answer: testData.answer,
          collection_id: testData.collection_id,
        })
        .select()
        .single();

      if (qaError) {
        throw new Error(`Failed to create Q&A pair: ${qaError.message}`);
      }

      testResults.qa_creation = {
        success: true,
        qa_id: qaPair.id,
        created_at: qaPair.created_at
      };
      testResults.steps.push('✅ Q&A pair created');
      console.log('✅ Step 3 completed: Q&A pair created');

      // Step 4: Sync to Qdrant
      console.log('🔄 Step 4: Syncing to Qdrant...');
      
      const qdrantResponse = await fetch('/api/qdrant-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: testData.question,
          answer: testData.answer,
          vector: embedding,
          collection_id: testData.collection_id,
          user_id: testData.user_id,
        }),
      });

      if (!qdrantResponse.ok) {
        const errorData = await qdrantResponse.json();
        throw new Error(`Qdrant sync failed: ${errorData.error}`);
      }

      const qdrantResult = await qdrantResponse.json();
      testResults.qdrant_sync = {
        success: qdrantResult.success,
        point_id: qdrantResult.pointId,
        collection_name: qdrantResult.collectionName
      };
      testResults.steps.push('✅ Vector synced to Qdrant');
      console.log('✅ Step 4 completed: Vector synced to Qdrant');

      // Step 5: Test search embedding generation
      console.log('🔄 Step 5: Testing search embedding generation...');
      
      const searchQuery = "quality";
      const searchEmbedding = await generateSearchEmbedding(searchQuery);
      
      testResults.search_embedding = {
        success: true,
        query: searchQuery,
        dimensions: searchEmbedding.length,
        magnitude: Math.sqrt(searchEmbedding.reduce((sum, val) => sum + val * val, 0)),
        is_zero_vector: searchEmbedding.every(v => v === 0)
      };
      testResults.steps.push('✅ Search embedding generated');
      console.log('✅ Step 5 completed: Search embedding generated');

      // Step 6: Test RAG search
      console.log('🔄 Step 6: Testing RAG search...');
      
      const searchResponse = await fetch('/api/rag-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          collection_id: testData.collection_id,
          user_id: testData.user_id,
          vector: searchEmbedding,
          limit: 5,
          threshold: 0.1
        }),
      });

      if (!searchResponse.ok) {
        const errorData = await searchResponse.json();
        throw new Error(`RAG search failed: ${errorData.error}`);
      }

      const searchResult = await searchResponse.json();
      testResults.rag_search = {
        success: searchResult.success,
        total_results: searchResult.total_results,
        results_found: searchResult.results?.length || 0,
        top_score: searchResult.results?.[0]?.score || 0
      };
      testResults.steps.push('✅ RAG search completed');
      console.log('✅ Step 6 completed: RAG search completed');

      // Step 7: Test RAG chat (if Gemini is configured)
      if (process.env.GEMINI_API_KEY) {
        console.log('🔄 Step 7: Testing RAG chat...');
        
        const chatResponse = await fetch('/api/rag-chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            question: searchQuery,
            collection_id: testData.collection_id,
            user_id: testData.user_id,
            query_vector: searchEmbedding,
            language: 'en'
          }),
        });

        if (chatResponse.ok) {
          const chatResult = await chatResponse.json();
          testResults.rag_chat = {
            success: chatResult.success,
            answer_generated: !!chatResult.answer,
            context_results: chatResult.context?.results_found || 0,
            has_context: chatResult.context?.has_context || false
          };
          testResults.steps.push('✅ RAG chat completed');
          console.log('✅ Step 7 completed: RAG chat completed');
        } else {
          testResults.rag_chat = {
            success: false,
            error: 'RAG chat failed'
          };
          testResults.steps.push('⚠️ RAG chat failed (non-critical)');
        }
      } else {
        testResults.rag_chat = {
          success: false,
          error: 'Gemini API key not configured'
        };
        testResults.steps.push('⚠️ RAG chat skipped (Gemini not configured)');
      }

      testResults.overall_success = true;

    } catch (error) {
      console.error('❌ Test failed:', error);
      testResults.steps.push(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // Cleanup: Delete test data
      console.log('🔄 Cleanup: Removing test data...');
      
      try {
        // Delete Q&A pairs
        await supabase
          .from('qa_pairs')
          .delete()
          .eq('collection_id', testData.collection_id);

        // Delete collection
        await supabase
          .from('collections')
          .delete()
          .eq('id', testData.collection_id);

        // Clean up Qdrant collection if it was created
        if (testResults.qdrant_sync?.success) {
          try {
            await fetch('/api/qdrant-delete', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                collection_id: testData.collection_id,
                user_id: testData.user_id,
              }),
            });
          } catch (qdrantCleanupError) {
            console.warn('⚠️ Qdrant cleanup failed:', qdrantCleanupError);
          }
        }

        testResults.cleanup = { success: true };
        testResults.steps.push('✅ Test data cleaned up');
        console.log('✅ Cleanup completed');

      } catch (cleanupError) {
        console.error('❌ Cleanup failed:', cleanupError);
        testResults.cleanup = { 
          success: false, 
          error: cleanupError instanceof Error ? cleanupError.message : 'Unknown error' 
        };
        testResults.steps.push('⚠️ Cleanup partially failed');
      }
    }

    const totalTime = Date.now() - startTime;
    testResults.performance = {
      total_time_ms: totalTime,
      embedding_time_ms: testResults.embedding_generation?.generation_time_ms || 0,
      steps_completed: testResults.steps.filter(step => step.startsWith('✅')).length,
      steps_failed: testResults.steps.filter(step => step.startsWith('❌')).length,
      steps_warned: testResults.steps.filter(step => step.startsWith('⚠️')).length
    };

    return NextResponse.json({
      success: testResults.overall_success,
      message: testResults.overall_success 
        ? 'Complete RAG workflow test passed successfully' 
        : 'RAG workflow test encountered issues',
      results: testResults,
      test_data: {
        question: testData.question,
        answer: testData.answer,
        collection_id: testData.collection_id
      }
    });

  } catch (error) {
    console.error('❌ Complete RAG workflow test failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Test execution failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
