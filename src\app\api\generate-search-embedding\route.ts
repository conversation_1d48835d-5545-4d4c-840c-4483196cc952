import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    const body = await request.json();
    const { query } = body;

    // Validate input
    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Generating search embedding server-side for query...');

    // Import transformers library
    const { pipeline } = await import('@xenova/transformers');
    
    // Initialize embedding pipeline
    const embeddingPipeline = await pipeline(
      'feature-extraction',
      'Xenova/all-MiniLM-L6-v2',
      {
        device: 'cpu', // Use CPU on server
        dtype: 'fp32',
      }
    );

    // For search queries, we format it as a query
    const searchText = `Query: ${query}`;
    
    console.log('🔄 Processing search query with embedding model...');
    
    // Generate embedding
    const result = await embeddingPipeline(searchText, {
      pooling: 'mean',
      normalize: true,
    });

    // Extract the embedding vector
    const embedding = Array.from(result.data) as number[];
    
    console.log('✅ Server-side search embedding generated:', {
      dimensions: embedding.length,
      query_length: query.length,
      user_id: user.id
    });

    // Validate embedding
    if (!embedding || embedding.length === 0) {
      throw new Error('Generated embedding is empty');
    }
    
    if (embedding.some(val => !isFinite(val))) {
      throw new Error('Generated embedding contains invalid values');
    }

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'server-side',
      model: 'Xenova/all-MiniLM-L6-v2',
      text_length: searchText.length,
      type: 'search-query',
    });

  } catch (error) {
    console.error('❌ Server-side search embedding generation failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate search embedding',
        details: error instanceof Error ? error.message : 'Unknown error',
        method: 'server-side',
        type: 'search-query'
      },
      { status: 500 }
    );
  }
}
