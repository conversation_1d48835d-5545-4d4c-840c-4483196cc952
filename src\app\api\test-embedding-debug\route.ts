import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Embedding debug endpoint ready',
    instructions: 'This endpoint helps debug client-side embedding issues',
    browser_requirements: {
      webgpu: 'Optional - falls back to CPU if not available',
      wasm: 'Required for @xenova/transformers',
      memory: 'At least 2GB RAM recommended for model loading'
    },
    troubleshooting: {
      'Failed to generate embedding': [
        'Check browser console for detailed error messages',
        'Ensure sufficient memory is available',
        'Try disabling WebGPU by setting device to "cpu"',
        'Check network connectivity for model download'
      ],
      'Model loading issues': [
        'Clear browser cache and reload',
        'Check if CORS is properly configured',
        'Verify CDN access for model files'
      ]
    }
  });
}

export async function POST(request: Request) {
  try {
    const { test_type = 'basic' } = await request.json();
    
    return NextResponse.json({
      success: true,
      message: 'Server-side embedding test endpoint',
      test_type,
      note: 'Actual embedding generation happens client-side using @xenova/transformers',
      client_test_instructions: [
        'Open browser developer tools',
        'Go to /dashboard/rag-test page',
        'Try to generate an embedding',
        'Check console for detailed error messages',
        'Look for network requests to CDN for model files'
      ],
      common_issues: {
        'CORS errors': 'Model files cannot be loaded from CDN',
        'Memory errors': 'Insufficient browser memory for model',
        'WebGPU errors': 'GPU acceleration issues - will fallback to CPU',
        'Network errors': 'Cannot download model files'
      }
    });

  } catch (error) {
    return NextResponse.json(
      { 
        success: false,
        error: 'Invalid request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 400 }
    );
  }
}
