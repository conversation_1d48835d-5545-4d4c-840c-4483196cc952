import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Transformers library test endpoint',
    note: 'This tests if @xenova/transformers can be imported and used',
    client_side_only: true,
    instructions: 'POST to this endpoint to test transformers import'
  });
}

export async function POST() {
  try {
    // Try to import the transformers library
    console.log('🧪 Testing @xenova/transformers import...');
    
    // This should fail on server-side, which is expected
    try {
      const { pipeline } = await import('@xenova/transformers');
      console.log('✅ @xenova/transformers imported successfully on server');
      
      // Try to create a pipeline (this will likely fail on server)
      try {
        const testPipeline = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
        console.log('✅ Pipeline created successfully on server');
        
        return NextResponse.json({
          success: true,
          message: 'Transformers library working on server (unexpected but good)',
          server_side: true,
          pipeline_created: true
        });
      } catch (pipelineError) {
        console.log('⚠️ Pipeline creation failed on server (expected):', pipelineError);
        
        return NextResponse.json({
          success: true,
          message: 'Transformers library imported but pipeline creation failed on server (expected)',
          server_side: true,
          pipeline_created: false,
          pipeline_error: pipelineError instanceof Error ? pipelineError.message : 'Unknown error'
        });
      }
    } catch (importError) {
      console.log('⚠️ Transformers import failed on server (expected):', importError);
      
      return NextResponse.json({
        success: true,
        message: 'Transformers import failed on server (expected - should work client-side)',
        server_side: true,
        import_failed: true,
        import_error: importError instanceof Error ? importError.message : 'Unknown error',
        note: 'This is expected - transformers should work client-side only'
      });
    }

  } catch (error) {
    console.error('❌ Transformers test failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Transformers test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      server_side: true
    }, { status: 500 });
  }
}
