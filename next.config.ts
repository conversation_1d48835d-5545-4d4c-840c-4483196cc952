import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Experimental features (removed esmExternals for Turbopack compatibility)
  experimental: {
    // esmExternals: 'loose', // Not supported by Turbopack
  },

  // Webpack configuration for @xenova/transformers
  webpack: (config, { isServer }) => {
    // Handle WASM files for transformers
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      layers: true,
    };

    // Handle .wasm files
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    });

    // Externalize onnxruntime-node for server-side
    if (isServer) {
      config.externals.push('onnxruntime-node');
    }

    // Resolve fallbacks for Node.js modules in browser
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };

    return config;
  },

  // Headers for better compatibility (commented out as they can be restrictive)
  // async headers() {
  //   return [
  //     {
  //       source: '/(.*)',
  //       headers: [
  //         {
  //           key: 'Cross-Origin-Embedder-Policy',
  //           value: 'require-corp',
  //         },
  //         {
  //           key: 'Cross-Origin-Opener-Policy',
  //           value: 'same-origin',
  //         },
  //       ],
  //     },
  //   ];
  // },
};

export default nextConfig;
