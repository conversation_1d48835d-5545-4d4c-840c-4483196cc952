<!DOCTYPE html>
<html>
<head>
    <title>Test Embedding Generation</title>
</head>
<body>
    <h1>Test Embedding Generation</h1>
    <button onclick="testEmbedding()">Test Embedding</button>
    <div id="output"></div>

    <script type="module">
        async function testEmbedding() {
            const output = document.getElementById('output');
            output.innerHTML = 'Testing embedding generation...';
            
            try {
                // Import the transformers library
                const { pipeline } = await import('https://cdn.jsdelivr.net/npm/@xenova/transformers@2.17.2/dist/transformers.min.js');
                
                output.innerHTML += '<br>✅ Transformers library loaded';
                
                // Initialize the embedding pipeline
                const embeddingPipeline = await pipeline(
                    'feature-extraction',
                    'Xenova/all-MiniLM-L6-v2',
                    {
                        device: 'webgpu',
                        dtype: 'fp32',
                    }
                );
                
                output.innerHTML += '<br>✅ Embedding pipeline initialized';
                
                // Test text
                const testText = 'Question: What is the capital of France?\nAnswer: The capital of France is Paris.';
                
                // Generate embedding
                const result = await embeddingPipeline(testText, {
                    pooling: 'mean',
                    normalize: true,
                });
                
                const embedding = Array.from(result.data);
                
                output.innerHTML += `<br>✅ Embedding generated successfully`;
                output.innerHTML += `<br>📊 Dimensions: ${embedding.length}`;
                output.innerHTML += `<br>📊 First 5 values: [${embedding.slice(0, 5).map(x => x.toFixed(4)).join(', ')}]`;
                
                // Now test the sync API
                output.innerHTML += '<br><br>Testing Qdrant sync API...';
                
                const syncData = {
                    question: "What is the capital of France?",
                    answer: "The capital of France is Paris.",
                    vector: embedding,
                    collection_id: "test-collection-id",
                    user_id: "test-user-id"
                };
                
                const response = await fetch('/api/qdrant-sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(syncData)
                });
                
                const result_sync = await response.json();
                
                if (response.ok) {
                    output.innerHTML += '<br>✅ Qdrant sync successful';
                    output.innerHTML += `<br>📊 Response: ${JSON.stringify(result_sync, null, 2)}`;
                } else {
                    output.innerHTML += '<br>❌ Qdrant sync failed';
                    output.innerHTML += `<br>📊 Status: ${response.status}`;
                    output.innerHTML += `<br>📊 Error: ${JSON.stringify(result_sync, null, 2)}`;
                }
                
            } catch (error) {
                output.innerHTML += `<br>❌ Error: ${error.message}`;
                console.error('Test failed:', error);
            }
        }
        
        // Make function global
        window.testEmbedding = testEmbedding;
    </script>
</body>
</html>
