import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';
import { APP_CONFIG } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured. Vector search is not available.' },
        { status: 503 }
      );
    }

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const user = await getCurrentUser();
    
    const body = await request.json();
    const { 
      query, 
      collection_id, 
      user_id, 
      vector, 
      limit = APP_CONFIG.rag.maxSearchResults,
      threshold = APP_CONFIG.rag.similarityThreshold 
    } = body;

    // Validate required fields
    if (!query || !collection_id || !user_id || !vector) {
      return NextResponse.json(
        { error: 'Missing required fields: query, collection_id, user_id, vector' },
        { status: 400 }
      );
    }

    // Ensure the user can only search their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Validate vector format
    if (!Array.isArray(vector) || vector.length === 0) {
      return NextResponse.json(
        { error: 'Vector must be a non-empty array' },
        { status: 400 }
      );
    }

    // Create collection name for Qdrant
    const qdrantCollectionName = `user_${user_id}_collection_${collection_id}`;

    console.log('🔍 RAG search request:', {
      user_id,
      collection_id,
      query_length: query.length,
      vector_length: vector.length,
      limit,
      threshold,
      collection_name: qdrantCollectionName
    });

    try {
      // Check if collection exists
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        return NextResponse.json({
          success: true,
          results: [],
          message: 'Collection not found or empty',
          query,
          collection_id,
        });
      }

      // Perform vector search
      const searchResult = await qdrantClient.search(qdrantCollectionName, {
        vector: vector,
        limit: limit,
        score_threshold: threshold,
        with_payload: true,
      });

      // Format results
      const results = searchResult.map((point) => ({
        id: point.id,
        score: point.score,
        question: point.payload?.question,
        answer: point.payload?.answer,
        created_at: point.payload?.created_at,
      }));

      console.log('✅ RAG search completed:', {
        results_count: results.length,
        top_score: results[0]?.score || 0,
      });

      return NextResponse.json({
        success: true,
        results,
        query,
        collection_id,
        total_results: results.length,
        search_params: {
          limit,
          threshold,
          vector_dimensions: vector.length,
        },
      });

    } catch (qdrantError) {
      console.error('Qdrant search failed:', qdrantError);
      return NextResponse.json(
        { 
          error: 'Failed to search in Qdrant', 
          details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in rag-search API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
