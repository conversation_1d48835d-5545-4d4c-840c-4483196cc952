import { NextResponse } from 'next/server';

export async function GET() {
  const documentation = {
    title: 'odude-chat n8n API Documentation',
    version: '1.0.0',
    description: 'API endpoints for integrating odude-chat RAG functionality with n8n workflows',
    base_url: 'https://your-domain.com/api/n8n',
    
    authentication: {
      type: 'API Key',
      header: 'x-api-key',
      alternative: 'Authorization: Bearer <api-key>',
      note: 'Set N8N_API_KEY environment variable on the server'
    },

    endpoints: {
      status: {
        method: 'GET',
        path: '/api/n8n/status',
        description: 'Check service status and availability',
        headers: {
          'x-api-key': 'your-api-key'
        },
        response: {
          success: true,
          status: 'healthy|warning|error',
          message: 'Status message',
          services: {
            qdrant: { status: 'healthy', message: 'Connected successfully' },
            gemini: { status: 'healthy', message: 'API key valid and working' }
          },
          endpoints: {},
          supported_languages: [],
          timestamp: '2025-08-04T17:00:00.000Z'
        }
      },

      'rag-search': {
        method: 'POST',
        path: '/api/n8n/rag-search',
        description: 'Search for similar Q&A pairs using vector similarity',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'your-api-key'
        },
        body: {
          collection_id: 'string (required) - ID of the Q&A collection',
          user_id: 'string (required) - ID of the user who owns the collection',
          query: 'string (optional) - Text query to search for',
          vector: 'number[] (optional) - Pre-computed embedding vector',
          limit: 'number (optional) - Maximum results to return (default: 5)',
          threshold: 'number (optional) - Minimum similarity score (default: 0.7)'
        },
        response: {
          success: true,
          results: [
            {
              id: 'point-id',
              score: 0.95,
              question: 'What is the capital of France?',
              answer: 'The capital of France is Paris.',
              created_at: '2025-08-04T17:00:00.000Z'
            }
          ],
          total_results: 1,
          search_params: { limit: 5, threshold: 0.7 }
        }
      },

      'rag-chat': {
        method: 'POST',
        path: '/api/n8n/rag-chat',
        description: 'Generate AI responses based on Q&A collection context',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'your-api-key'
        },
        body: {
          question: 'string (required) - Question to answer',
          collection_id: 'string (required) - ID of the Q&A collection',
          user_id: 'string (required) - ID of the user who owns the collection',
          query_vector: 'number[] (optional) - Pre-computed embedding vector for the question',
          language: 'string (optional) - Response language: "en", "hi-en", "ne-en" (default: "en")',
          limit: 'number (optional) - Maximum context results to use (default: 5)',
          threshold: 'number (optional) - Minimum similarity score for context (default: 0.7)'
        },
        response: {
          success: true,
          answer: 'The capital of France is Paris, a beautiful city known for the Eiffel Tower.',
          question: 'What is the capital of France?',
          language: 'en',
          context: {
            results_found: 1,
            search_results: [
              {
                id: 'point-id',
                score: 0.95,
                question: 'What is the capital of France?',
                answer: 'The capital of France is Paris.'
              }
            ],
            has_context: true
          },
          metadata: {
            collection_id: 'collection-123',
            user_id: 'user-456',
            model: 'gemini-1.5-flash',
            timestamp: '2025-08-04T17:00:00.000Z'
          }
        }
      }
    },

    error_responses: {
      '401': {
        success: false,
        error: 'Unauthorized. Please provide a valid API key in the x-api-key header.'
      },
      '400': {
        success: false,
        error: 'Missing required fields: question, collection_id, user_id',
        code: 'MISSING_REQUIRED_FIELDS',
        required_fields: ['question', 'collection_id', 'user_id']
      },
      '404': {
        success: false,
        error: 'Collection not found. Please ensure the collection exists and has Q&A pairs.',
        code: 'COLLECTION_NOT_FOUND'
      },
      '500': {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      '503': {
        success: false,
        error: 'Qdrant is not configured. Vector search is not available.',
        code: 'QDRANT_NOT_CONFIGURED'
      }
    },

    supported_languages: [
      { code: 'en', name: 'English', description: 'Pure English responses' },
      { code: 'hi-en', name: 'Hindi-English', description: 'Mixed Hindi and English responses' },
      { code: 'ne-en', name: 'Nepali-English', description: 'Mixed Nepali and English responses' }
    ],

    examples: {
      'n8n_workflow_example': {
        description: 'Example n8n workflow configuration',
        steps: [
          {
            node: 'HTTP Request',
            method: 'POST',
            url: '{{$node["Webhook"].json["base_url"]}}/api/n8n/rag-chat',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': '{{$node["Set"].json["api_key"]}}'
            },
            body: {
              question: '{{$node["Webhook"].json["question"]}}',
              collection_id: '{{$node["Set"].json["collection_id"]}}',
              user_id: '{{$node["Set"].json["user_id"]}}',
              language: 'en'
            }
          }
        ]
      }
    },

    notes: [
      'API keys should be kept secure and not exposed in client-side code',
      'Vector embeddings are optional - the API can work with text queries alone',
      'Collections must exist and contain Q&A pairs before they can be searched',
      'The service requires Qdrant and Gemini API to be properly configured',
      'Rate limiting may be implemented in future versions'
    ]
  };

  return NextResponse.json(documentation, {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type, x-api-key, Authorization'
    }
  });
}
