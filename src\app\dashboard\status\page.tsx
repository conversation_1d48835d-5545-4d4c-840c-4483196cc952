import { getCurrentUser } from '@/lib/auth';
import { 
  Title, 
  Text, 
  Card, 
  Group, 
  Stack, 
  Badge,
  SimpleGrid,
  Progress,
  Divider,
  Code,
  Alert,
} from '@mantine/core';
import { 
  IconDatabase, 
  IconBrain,
  IconServer,
  IconCheck,
  IconX,
  IconAlertTriangle,
  IconInfoCircle,
} from '@tabler/icons-react';
import { supabase } from '@/lib/supabase';

interface StatusCheck {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details?: any;
  icon: React.ComponentType<any>;
}

async function checkSupabaseConnection(): Promise<StatusCheck> {
  try {
    const { data, error } = await supabase
      .from('collections')
      .select('count', { count: 'exact', head: true });

    if (error) {
      return {
        name: 'Supabase Database',
        status: 'error',
        message: `Connection failed: ${error.message}`,
        icon: IconDatabase,
      };
    }

    return {
      name: 'Supabase Database',
      status: 'healthy',
      message: `Connected successfully. Total collections: ${data || 0}`,
      details: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      icon: IconDatabase,
    };
  } catch (error) {
    return {
      name: 'Supabase Database',
      status: 'error',
      message: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: IconDatabase,
    };
  }
}

async function checkQdrantConnection(): Promise<StatusCheck> {
  // Check if environment variables are set
  if (!process.env.QDRANT_URL) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: 'QDRANT_URL environment variable not set',
      details: {
        note: 'Qdrant is optional for basic functionality',
        required_env: ['QDRANT_URL', 'QDRANT_API_KEY (optional)']
      },
      icon: IconBrain,
    };
  }

  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-qdrant`);
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'Qdrant Vector Database',
        status: 'warning',
        message: result.error || 'Connection failed - Vector search disabled',
        details: {
          ...result.details,
          note: 'App will work without vector search functionality'
        },
        icon: IconBrain,
      };
    }

    return {
      name: 'Qdrant Vector Database',
      status: 'healthy',
      message: `Connected successfully. Collections: ${result.collections?.length || 0}`,
      details: {
        url: result.qdrantUrl,
        hasApiKey: result.hasApiKey,
        collections: result.collections,
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: `Connection error - Vector search disabled`,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'App will work without vector search functionality'
      },
      icon: IconBrain,
    };
  }
}

async function checkQdrantSyncStatus(): Promise<StatusCheck> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/test-qa-sync`, { method: 'POST' });
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'Qdrant Sync Workflow',
        status: 'error',
        message: result.error || 'Q&A sync test failed',
        details: result.details || result,
        icon: IconBrain,
      };
    }

    return {
      name: 'Qdrant Sync Workflow',
      status: 'healthy',
      message: `Q&A sync working correctly`,
      details: {
        workflow: result.results?.workflow || [],
        vectorDimensions: result.results?.vectorDimensions,
        searchResults: result.results?.searchResults,
        testCompleted: true
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'Qdrant Sync Workflow',
      status: 'error',
      message: 'Failed to test Q&A sync workflow',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'This affects Q&A pair creation and vector search'
      },
      icon: IconBrain,
    };
  }
}

async function checkRAGChatStatus(): Promise<StatusCheck> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/test-rag-chat`, { method: 'POST' });
    const result = await response.json();

    if (!response.ok || !result.success) {
      return {
        name: 'RAG Chat Functionality',
        status: 'error',
        message: result.error || 'RAG chat test failed',
        details: result.details || result,
        icon: IconBrain,
      };
    }

    return {
      name: 'RAG Chat Functionality',
      status: 'healthy',
      message: `RAG chat working correctly`,
      details: {
        workflow: result.results?.workflow || [],
        testQAPairs: result.results?.testQAPairs,
        searchResults: result.results?.searchResults,
        ragAnswerGenerated: !!result.results?.ragAnswer,
        testCompleted: true
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: 'RAG Chat Functionality',
      status: 'error',
      message: 'Failed to test RAG chat functionality',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        note: 'This affects AI-powered question answering'
      },
      icon: IconBrain,
    };
  }
}

async function checkGeminiAPIStatus(): Promise<StatusCheck> {
  if (!process.env.GEMINI_API_KEY) {
    return {
      name: 'Gemini AI API',
      status: 'warning',
      message: 'GEMINI_API_KEY not configured',
      details: {
        note: 'Required for RAG chat functionality',
        required_env: ['GEMINI_API_KEY']
      },
      icon: IconBrain,
    };
  }

  return {
    name: 'Gemini AI API',
    status: 'healthy',
    message: 'API key configured',
    details: {
      hasApiKey: true,
      model: 'gemini-1.5-flash',
      note: 'Used for generating AI responses in RAG chat'
    },
    icon: IconBrain,
  };
}

async function checkTransformersStatus(): Promise<StatusCheck> {
  try {
    const response = await fetch('/api/test-embedding');
    const result = await response.json();

    if (!response.ok) {
      return {
        name: '@xenova/transformers',
        status: 'healthy',
        message: 'Client-side embedding model ready (test endpoint optional)',
        details: {
          model: 'Xenova/all-MiniLM-L6-v2',
          type: 'Client-side processing',
          note: 'Model loads automatically when needed',
          status: 'Test endpoint not responding but functionality available'
        },
        icon: IconBrain,
      };
    }

    return {
      name: '@xenova/transformers',
      status: 'healthy',
      message: 'Client-side embedding model available',
      details: {
        model: 'Xenova/all-MiniLM-L6-v2',
        type: 'Client-side processing',
        note: 'Model loads on first use in browser',
        test_endpoint: 'Responding correctly'
      },
      icon: IconBrain,
    };
  } catch (error) {
    return {
      name: '@xenova/transformers',
      status: 'healthy',
      message: 'Client-side embedding model ready (test endpoint unavailable)',
      details: {
        model: 'Xenova/all-MiniLM-L6-v2',
        type: 'Client-side processing',
        note: 'Model loads automatically when needed',
        test_error: error instanceof Error ? error.message : 'Unknown error'
      },
      icon: IconBrain,
    };
  }
}

async function getSystemInfo() {
  const qaCount = await supabase
    .from('qa_pairs')
    .select('count', { count: 'exact', head: true });

  const userCount = await supabase
    .from('users')
    .select('count', { count: 'exact', head: true });

  return {
    totalQAPairs: qaCount.count || 0,
    totalUsers: userCount.count || 0,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  };
}

export default async function StatusPage() {
  // Ensure user is authenticated
  await getCurrentUser();

  const [supabaseStatus, qdrantStatus, transformersStatus, qdrantSyncStatus, ragChatStatus, geminiStatus, systemInfo] = await Promise.all([
    checkSupabaseConnection(),
    checkQdrantConnection(),
    checkTransformersStatus(),
    checkQdrantSyncStatus(),
    checkRAGChatStatus(),
    checkGeminiAPIStatus(),
    getSystemInfo(),
  ]);

  const allChecks = [supabaseStatus, qdrantStatus, transformersStatus, qdrantSyncStatus, ragChatStatus, geminiStatus];
  const healthyCount = allChecks.filter(check => check.status === 'healthy').length;
  const overallHealth = healthyCount === allChecks.length ? 'healthy' : 
                       healthyCount > 0 ? 'warning' : 'error';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return IconCheck;
      case 'warning': return IconAlertTriangle;
      case 'error': return IconX;
      default: return IconInfoCircle;
    }
  };

  return (
    <Stack gap="lg">
      <div>
        <Title order={1}>System Status</Title>
        <Text c="dimmed" size="lg">
          Real-time status of all system components and connections
        </Text>
      </div>

      {/* Overall Health */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group justify="space-between" mb="md">
          <Group gap="sm">
            <IconServer size="1.5rem" />
            <Title order={3}>Overall System Health</Title>
          </Group>
          <Badge 
            color={getStatusColor(overallHealth)} 
            variant="filled" 
            size="lg"
          >
            {overallHealth.toUpperCase()}
          </Badge>
        </Group>
        
        <Progress 
          value={(healthyCount / allChecks.length) * 100} 
          color={getStatusColor(overallHealth)}
          size="lg"
          mb="sm"
        />
        
        <Text size="sm" c="dimmed">
          {healthyCount} of {allChecks.length} services operational
        </Text>
      </Card>

      {/* Service Status */}
      <SimpleGrid cols={{ base: 1, md: 2, lg: 3 }} spacing="lg">
        {allChecks.map((check, index) => {
          const StatusIcon = getStatusIcon(check.status);
          const ServiceIcon = check.icon;
          
          return (
            <Card key={index} shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Group gap="xs">
                  <ServiceIcon size="1.2rem" />
                  <Text fw={500} size="sm">{check.name}</Text>
                </Group>
                <StatusIcon 
                  size="1rem" 
                  color={`var(--mantine-color-${getStatusColor(check.status)}-6)`}
                />
              </Group>
              
              <Text size="xs" c="dimmed" mb="md">
                {check.message}
              </Text>
              
              <Badge 
                color={getStatusColor(check.status)} 
                variant="light" 
                size="sm"
              >
                {check.status.toUpperCase()}
              </Badge>
              
              {check.details && (
                <div style={{ marginTop: '12px' }}>
                  <Divider size="xs" mb="xs" />
                  <Code block style={{ fontSize: '10px' }}>
                    {JSON.stringify(check.details, null, 2)}
                  </Code>
                </div>
              )}
            </Card>
          );
        })}
      </SimpleGrid>

      {/* System Information */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group gap="sm" mb="md">
          <IconInfoCircle size="1.2rem" />
          <Title order={3}>System Information</Title>
        </Group>
        
        <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
          <div>
            <Text size="sm" fw={500}>Total Q&A Pairs</Text>
            <Text size="xl" fw={700} c="blue">{systemInfo.totalQAPairs}</Text>
          </div>
          <div>
            <Text size="sm" fw={500}>Total Users</Text>
            <Text size="xl" fw={700} c="green">{systemInfo.totalUsers}</Text>
          </div>
          <div>
            <Text size="sm" fw={500}>Environment</Text>
            <Badge variant="outline">{systemInfo.environment}</Badge>
          </div>
          <div>
            <Text size="sm" fw={500}>Last Updated</Text>
            <Text size="xs" c="dimmed">
              {new Date(systemInfo.timestamp).toLocaleString()}
            </Text>
          </div>
        </SimpleGrid>
      </Card>

      <Alert icon={<IconInfoCircle size="1rem" />} color="blue">
        <Text size="sm">
          This page automatically checks the status of all system components.
          Refresh the page to get the latest status information.
        </Text>
      </Alert>

      {/* Setup Instructions */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Title order={4} mb="md">Setup Instructions</Title>
        <Stack gap="sm">
          <div>
            <Text fw={500} size="sm">To enable Qdrant vector search:</Text>
            <Text size="xs" c="dimmed">
              1. Set up a Qdrant instance (local or cloud)<br/>
              2. Add QDRANT_URL to your environment variables<br/>
              3. Optionally add QDRANT_API_KEY for authentication
            </Text>
          </div>
          <div>
            <Text fw={500} size="sm">Core functionality:</Text>
            <Text size="xs" c="dimmed">
              The app works fully without Qdrant - it only enhances search capabilities with semantic vector search.
            </Text>
          </div>
        </Stack>
      </Card>
    </Stack>
  );
}
