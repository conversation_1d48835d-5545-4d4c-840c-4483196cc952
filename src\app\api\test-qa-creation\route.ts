import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await getCurrentUser();
    
    console.log('🧪 Testing Q&A creation workflow for user:', user.id);

    const body = await request.json();
    const { question, answer, collection_id } = body;

    // Validate input
    if (!question || !answer || !collection_id) {
      return NextResponse.json(
        { error: 'Question, answer, and collection_id are required' },
        { status: 400 }
      );
    }

    console.log('📝 Creating Q&A pair:', {
      question: question.substring(0, 50) + '...',
      answer: answer.substring(0, 50) + '...',
      collection_id,
      user_id: user.id
    });

    const results = {
      steps: [],
      qa_creation: null,
      embedding_generation: null,
      qdrant_sync: null,
      overall_success: false
    };

    try {
      // Step 1: Create Q&A pair in Supabase
      console.log('🔄 Step 1: Creating Q&A pair in database...');
      
      const { data: qaData, error: qaError } = await supabase
        .from('qa_pairs')
        .insert({
          question,
          answer,
          collection_id,
          user_id: user.id,
        })
        .select()
        .single();

      if (qaError) {
        throw new Error(`Failed to create Q&A pair: ${qaError.message}`);
      }

      results.qa_creation = {
        success: true,
        qa_id: qaData.id,
        created_at: qaData.created_at
      };
      results.steps.push('✅ Q&A pair created in database');
      console.log('✅ Step 1 completed: Q&A pair created');

      // Step 2: Generate embedding using the hybrid approach
      console.log('🔄 Step 2: Generating embedding...');
      
      try {
        // Try client-side approach first (this will likely fail and fallback to server-side)
        const embeddingResponse = await fetch('/api/generate-embedding', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ question, answer }),
        });

        if (!embeddingResponse.ok) {
          throw new Error(`Embedding API failed: ${embeddingResponse.status}`);
        }

        const embeddingResult = await embeddingResponse.json();
        if (!embeddingResult.success) {
          throw new Error(`Embedding generation failed: ${embeddingResult.error}`);
        }

        results.embedding_generation = {
          success: true,
          dimensions: embeddingResult.dimensions,
          method: embeddingResult.method,
          model: embeddingResult.model
        };
        results.steps.push('✅ Embedding generated successfully');
        console.log('✅ Step 2 completed: Embedding generated');

        // Step 3: Sync to Qdrant
        console.log('🔄 Step 3: Syncing to Qdrant...');
        
        const qdrantResponse = await fetch('/api/qdrant-sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            question,
            answer,
            vector: embeddingResult.embedding,
            collection_id,
            user_id: user.id,
          }),
        });

        if (!qdrantResponse.ok) {
          throw new Error(`Qdrant sync failed: ${qdrantResponse.status}`);
        }

        const qdrantResult = await qdrantResponse.json();
        if (!qdrantResult.success) {
          throw new Error(`Qdrant sync failed: ${qdrantResult.error}`);
        }

        results.qdrant_sync = {
          success: true,
          collection_name: qdrantResult.collection_name,
          point_id: qdrantResult.point_id
        };
        results.steps.push('✅ Vector synced to Qdrant');
        console.log('✅ Step 3 completed: Vector synced to Qdrant');

        results.overall_success = true;

        return NextResponse.json({
          success: true,
          message: 'Q&A creation workflow completed successfully',
          qa_id: qaData.id,
          results,
          workflow_steps: [
            'Create Q&A pair in database',
            'Generate embedding (hybrid approach)',
            'Sync vector to Qdrant'
          ],
          performance: {
            embedding_method: embeddingResult.method,
            vector_dimensions: embeddingResult.dimensions,
            qdrant_collection: results.qdrant_sync.collection_name
          }
        });

      } catch (embeddingError) {
        console.error('❌ Embedding/Qdrant step failed:', embeddingError);
        
        // Q&A pair was created but embedding/sync failed
        return NextResponse.json({
          success: false,
          message: 'Q&A pair created but embedding/sync failed',
          qa_id: qaData.id,
          error: embeddingError instanceof Error ? embeddingError.message : 'Unknown error',
          completed_steps: results.steps,
          partial_results: results,
          note: 'Q&A pair exists in database but not in vector search'
        }, { status: 500 });
      }

    } catch (qaError) {
      console.error('❌ Q&A creation failed:', qaError);
      
      return NextResponse.json(
        { 
          success: false,
          error: 'Q&A creation failed',
          details: qaError instanceof Error ? qaError.message : 'Unknown error',
          completed_steps: results.steps
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Q&A creation test failed:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Test setup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
