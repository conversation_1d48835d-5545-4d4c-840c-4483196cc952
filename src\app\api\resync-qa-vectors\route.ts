import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { generateQAEmbedding } from '@/lib/embedding-client';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    const body = await request.json();
    const { collection_id, force_resync = false } = body;

    if (!collection_id) {
      return NextResponse.json(
        { error: 'collection_id is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Starting Q&A vector re-sync for collection:', collection_id);

    // Verify the collection belongs to the current user
    const { data: collection, error: collectionError } = await supabase
      .from('collections')
      .select('user_id, name')
      .eq('id', collection_id)
      .single();

    if (collectionError || !collection) {
      return NextResponse.json(
        { error: 'Collection not found' },
        { status: 404 }
      );
    }

    if (collection.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get all Q&A pairs for this collection
    const { data: qaPairs, error: qaPairsError } = await supabase
      .from('qa_pairs')
      .select('id, question, answer')
      .eq('collection_id', collection_id);

    if (qaPairsError) {
      return NextResponse.json(
        { error: 'Failed to fetch Q&A pairs' },
        { status: 500 }
      );
    }

    if (!qaPairs || qaPairs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No Q&A pairs found to sync',
        results: {
          total_pairs: 0,
          synced_pairs: 0,
          failed_pairs: 0,
          skipped_pairs: 0
        }
      });
    }

    console.log(`📊 Found ${qaPairs.length} Q&A pairs to process`);

    const results = {
      total_pairs: qaPairs.length,
      synced_pairs: 0,
      failed_pairs: 0,
      skipped_pairs: 0,
      details: [] as any[]
    };

    // Process each Q&A pair
    for (const qaPair of qaPairs) {
      try {
        console.log(`🔄 Processing Q&A pair ${qaPair.id}...`);

        // Generate embedding
        const vector = await generateQAEmbedding(qaPair.question, qaPair.answer);
        
        console.log(`✅ Generated embedding for Q&A pair ${qaPair.id}:`, {
          dimensions: vector.length,
          magnitude: Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0)).toFixed(4)
        });

        // Sync to Qdrant
        const syncResponse = await fetch('/api/qdrant-sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            question: qaPair.question,
            answer: qaPair.answer,
            vector,
            collection_id,
            user_id: user.id,
          }),
        });

        if (!syncResponse.ok) {
          const errorData = await syncResponse.json();
          throw new Error(errorData.error || `Sync failed with status ${syncResponse.status}`);
        }

        const syncResult = await syncResponse.json();
        if (!syncResult.success) {
          throw new Error(syncResult.error || 'Sync failed');
        }

        results.synced_pairs++;
        results.details.push({
          qa_id: qaPair.id,
          status: 'success',
          question: qaPair.question.substring(0, 50) + '...',
          vector_dimensions: vector.length,
          qdrant_point_id: syncResult.pointId
        });

        console.log(`✅ Successfully synced Q&A pair ${qaPair.id}`);

      } catch (error) {
        console.error(`❌ Failed to sync Q&A pair ${qaPair.id}:`, error);
        results.failed_pairs++;
        results.details.push({
          qa_id: qaPair.id,
          status: 'failed',
          question: qaPair.question.substring(0, 50) + '...',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('📊 Re-sync completed:', results);

    return NextResponse.json({
      success: true,
      message: `Re-sync completed for collection "${collection.name}"`,
      results,
      collection: {
        id: collection_id,
        name: collection.name,
        user_id: user.id
      }
    });

  } catch (error) {
    console.error('❌ Error in resync-qa-vectors API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
