'use client';

import { generateQAEmbedding } from './embedding-client';
import { APP_CONFIG } from './config';

export interface RAGSearchResult {
  id: string;
  score: number;
  question: string;
  answer: string;
  created_at: string;
}

export interface RAGSearchResponse {
  success: boolean;
  results: RAGSearchResult[];
  query: string;
  collection_id: string;
  total_results: number;
  search_params: {
    limit: number;
    threshold: number;
    vector_dimensions: number;
  };
  error?: string;
}

export interface RAGChatResponse {
  success: boolean;
  answer: string;
  question: string;
  language: string;
  context: {
    results_found: number;
    search_results: RAGSearchResult[];
    has_context: boolean;
  };
  metadata: {
    collection_id: string;
    search_params: { limit: number; threshold: number };
    model: string;
  };
  error?: string;
}

/**
 * Search for similar Q&A pairs using vector similarity
 */
export async function searchSimilarQA(data: {
  query: string;
  collection_id: string;
  user_id: string;
  limit?: number;
  threshold?: number;
}): Promise<RAGSearchResponse> {
  try {
    console.log('🔍 Generating embedding for search query...');
    
    // Generate embedding for the search query
    const queryVector = await generateQAEmbedding(data.query, '');

    console.log('🔍 Searching for similar Q&A pairs...');

    const response = await fetch('/api/rag-search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: data.query,
        collection_id: data.collection_id,
        user_id: data.user_id,
        vector: queryVector,
        limit: data.limit || APP_CONFIG.rag.maxSearchResults,
        threshold: data.threshold || APP_CONFIG.rag.similarityThreshold,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to search similar Q&A pairs');
    }

    console.log('✅ Search completed:', {
      results_found: result.total_results,
      query_length: data.query.length,
    });

    return result;

  } catch (error) {
    console.error('❌ Failed to search similar Q&A pairs:', error);
    throw error;
  }
}

/**
 * Generate RAG-based chat response
 */
export async function generateRAGResponse(data: {
  question: string;
  collection_id: string;
  user_id: string;
  language?: string;
  limit?: number;
  threshold?: number;
}): Promise<RAGChatResponse> {
  try {
    console.log('🤖 Generating RAG response...', {
      question_length: data.question.length,
      language: data.language || APP_CONFIG.rag.defaultLanguage,
      collection_id: data.collection_id,
    });
    
    // Generate embedding for the question
    const queryVector = await generateQAEmbedding(data.question, '');

    const response = await fetch('/api/rag-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question: data.question,
        collection_id: data.collection_id,
        user_id: data.user_id,
        query_vector: queryVector,
        language: data.language || APP_CONFIG.rag.defaultLanguage,
        limit: data.limit || APP_CONFIG.rag.maxSearchResults,
        threshold: data.threshold || APP_CONFIG.rag.similarityThreshold,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to generate RAG response');
    }

    console.log('✅ RAG response generated:', {
      answer_length: result.answer?.length || 0,
      context_results: result.context?.results_found || 0,
      language: result.language,
    });

    return result;

  } catch (error) {
    console.error('❌ Failed to generate RAG response:', error);
    throw error;
  }
}

/**
 * Get available language options for RAG chat
 */
export function getAvailableLanguages() {
  return APP_CONFIG.rag.supportedLanguages;
}

/**
 * Validate if a language code is supported
 */
export function isLanguageSupported(languageCode: string): boolean {
  return APP_CONFIG.rag.supportedLanguages.some(lang => lang.code === languageCode);
}
