import { NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';

export async function POST() {
  try {
    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured. Set QDRANT_URL environment variable.' },
        { status: 503 }
      );
    }

    console.log('🔄 Testing Qdrant upsert...');

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    // Generate test data
    const testVector = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
    const testCollectionName = 'test_collection_' + Date.now();

    // Generate a proper UUID for the point ID
    const crypto = require('crypto');
    const randomBytes = crypto.randomBytes(16);
    const testPointId = [
      randomBytes.toString('hex', 0, 4),
      randomBytes.toString('hex', 4, 6),
      randomBytes.toString('hex', 6, 8),
      randomBytes.toString('hex', 8, 10),
      randomBytes.toString('hex', 10, 16)
    ].join('-');

    console.log('📊 Test data:', {
      collectionName: testCollectionName,
      pointId: testPointId,
      vectorLength: testVector.length,
      vectorSample: testVector.slice(0, 3)
    });

    try {
      // Create test collection
      await qdrantClient.createCollection(testCollectionName, {
        vectors: {
          size: testVector.length,
          distance: 'Cosine',
        },
      });

      console.log('✅ Test collection created');

      // Test upsert
      const upsertData = {
        wait: true,
        points: [
          {
            id: testPointId,
            vector: testVector,
            payload: {
              question: 'Test question',
              answer: 'Test answer',
              test: true,
            },
          },
        ],
      };

      console.log('📊 Upserting test data...');
      await qdrantClient.upsert(testCollectionName, upsertData);
      console.log('✅ Upsert successful');

      // Clean up - delete the test collection
      await qdrantClient.deleteCollection(testCollectionName);
      console.log('✅ Test collection cleaned up');

      return NextResponse.json({
        success: true,
        message: 'Qdrant upsert test completed successfully',
        testData: {
          collectionName: testCollectionName,
          pointId: testPointId,
          vectorDimensions: testVector.length,
        },
      });

    } catch (qdrantError) {
      console.error('❌ Qdrant operation failed:', qdrantError);

      // Log the full error details if it's a response error
      if (qdrantError && typeof qdrantError === 'object' && 'data' in qdrantError) {
        console.error('❌ Qdrant error data:', JSON.stringify(qdrantError.data, null, 2));
      }

      // Try to clean up the test collection if it was created
      try {
        await qdrantClient.deleteCollection(testCollectionName);
        console.log('✅ Test collection cleaned up after error');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up test collection:', cleanupError);
      }

      return NextResponse.json(
        {
          error: 'Qdrant operation failed',
          details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error',
          qdrantErrorData: qdrantError && typeof qdrantError === 'object' && 'data' in qdrantError ? qdrantError.data : undefined,
          stack: qdrantError instanceof Error ? qdrantError.stack : undefined
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    return NextResponse.json(
      { 
        error: 'Test failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
