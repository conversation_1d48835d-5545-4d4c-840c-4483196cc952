import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    // Check if required services are configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured. Vector search is not available.' },
        { status: 503 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key is not configured.' },
        { status: 503 }
      );
    }

    // Initialize clients
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.rag.gemini.model });

    const user = await getCurrentUser();
    
    const body = await request.json();
    const { 
      question, 
      collection_id, 
      user_id, 
      query_vector,
      language = APP_CONFIG.rag.defaultLanguage,
      limit = APP_CONFIG.rag.maxSearchResults,
      threshold = APP_CONFIG.rag.similarityThreshold 
    } = body;

    // Validate required fields
    if (!question || !collection_id || !user_id || !query_vector) {
      return NextResponse.json(
        { error: 'Missing required fields: question, collection_id, user_id, query_vector' },
        { status: 400 }
      );
    }

    // Ensure the user can only access their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Validate language
    const supportedLanguages = APP_CONFIG.rag.supportedLanguages.map(lang => lang.code);
    if (!supportedLanguages.includes(language)) {
      return NextResponse.json(
        { error: `Unsupported language. Supported: ${supportedLanguages.join(', ')}` },
        { status: 400 }
      );
    }

    console.log('🤖 RAG chat request:', {
      user_id,
      collection_id,
      question_length: question.length,
      language,
      vector_length: query_vector.length,
    });

    try {
      // Step 1: Search for relevant context using vector similarity
      const qdrantCollectionName = `user_${user_id}_collection_${collection_id}`;
      
      // Check if collection exists
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      let context = '';
      let searchResults: any[] = [];

      if (collectionExists) {
        const searchResult = await qdrantClient.search(qdrantCollectionName, {
          vector: query_vector,
          limit: limit,
          score_threshold: threshold,
          with_payload: true,
        });

        searchResults = searchResult.map((point) => ({
          score: point.score,
          question: point.payload?.question,
          answer: point.payload?.answer,
        }));

        // Build context from search results
        context = searchResults
          .map((result, index) => 
            `Context ${index + 1} (relevance: ${(result.score * 100).toFixed(1)}%):\nQ: ${result.question}\nA: ${result.answer}`
          )
          .join('\n\n');
      }

      // Step 2: Generate language-specific prompt
      const languageInstructions = getLanguageInstructions(language);
      
      const prompt = `You are a helpful assistant answering questions based on the provided context. ${languageInstructions}

${context ? `CONTEXT:
${context}

` : 'No relevant context found in the knowledge base. '}USER QUESTION: ${question}

Please provide a helpful answer based on the context above. If the context doesn't contain relevant information, politely explain that you don't have specific information about this topic in the knowledge base.`;

      // Step 3: Generate response using Gemini
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const answer = response.text();

      console.log('✅ RAG chat completed:', {
        context_results: searchResults.length,
        answer_length: answer.length,
        language,
      });

      return NextResponse.json({
        success: true,
        answer,
        question,
        language,
        context: {
          results_found: searchResults.length,
          search_results: searchResults,
          has_context: context.length > 0,
        },
        metadata: {
          collection_id,
          search_params: { limit, threshold },
          model: APP_CONFIG.rag.gemini.model,
        },
      });

    } catch (error) {
      console.error('RAG chat processing failed:', error);
      return NextResponse.json(
        { 
          error: 'Failed to process RAG chat request', 
          details: error instanceof Error ? error.message : 'Unknown error' 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in rag-chat API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getLanguageInstructions(language: string): string {
  switch (language) {
    case 'hi-en':
      return 'Please respond in a mix of Hindi and English (Hinglish). Use Hindi words naturally mixed with English, as commonly spoken in India. Write in Roman script (English alphabet).';
    case 'ne-en':
      return 'Please respond in a mix of Nepali and English. Use Nepali words naturally mixed with English, as commonly spoken in Nepal. Write in Roman script (English alphabet).';
    case 'en':
    default:
      return 'Please respond in clear, professional English.';
  }
}
