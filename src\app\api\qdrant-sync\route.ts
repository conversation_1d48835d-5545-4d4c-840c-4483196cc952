import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { QdrantClient } from '@qdrant/js-client-rest';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Qdrant sync API called');

    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      console.error('❌ QDRANT_URL not configured');
      return NextResponse.json(
        { error: 'Qdrant is not configured. Set QDRANT_URL environment variable.' },
        { status: 503 }
      );
    }

    console.log('✅ Qdrant URL configured:', process.env.QDRANT_URL);

    // Initialize Qdrant client inside the function
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    console.log('✅ Qdrant client initialized');

    const user = await getCurrentUser();
    console.log('✅ User authenticated:', user.id);

    const body = await request.json();
    console.log('✅ Request body parsed, keys:', Object.keys(body));
    const { question, answer, vector, collection_id, user_id } = body;

    console.log('🔄 Qdrant sync request:', {
      user_id,
      collection_id,
      question_length: question?.length,
      answer_length: answer?.length,
      vector_length: vector?.length,
      vector_type: typeof vector,
      is_array: Array.isArray(vector)
    });

    // Validate required fields
    if (!question || !answer || !vector || !collection_id || !user_id) {
      return NextResponse.json(
        { error: 'Missing required fields: question, answer, vector, collection_id, user_id' },
        { status: 400 }
      );
    }

    // Ensure the user can only sync their own data
    if (user.id !== user_id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Validate vector format
    if (!Array.isArray(vector) || vector.length === 0) {
      return NextResponse.json(
        { error: 'Vector must be a non-empty array' },
        { status: 400 }
      );
    }

    // Validate vector values
    const invalidValues = vector.filter(val => typeof val !== 'number' || !isFinite(val));
    if (invalidValues.length > 0) {
      return NextResponse.json(
        { error: `Vector contains invalid values: ${invalidValues.slice(0, 5)}` },
        { status: 400 }
      );
    }

    // Check for zero vectors (all zeros) which indicate failed embedding generation
    const isZeroVector = vector.every(val => val === 0);
    if (isZeroVector) {
      return NextResponse.json(
        { error: 'Vector appears to be a zero vector, indicating failed embedding generation. Please regenerate the embedding.' },
        { status: 400 }
      );
    }

    // Check vector magnitude (should not be too small for normalized vectors)
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude < 0.1) {
      console.warn(`⚠️ Vector has very small magnitude: ${magnitude.toFixed(6)}`);
      return NextResponse.json(
        { error: `Vector magnitude is too small (${magnitude.toFixed(6)}), indicating potential embedding generation issues.` },
        { status: 400 }
      );
    }

    // Check expected dimensions (all-MiniLM-L6-v2 produces 384-dimensional vectors)
    if (vector.length !== 384) {
      console.warn(`⚠️ Unexpected vector dimensions: ${vector.length}, expected 384`);
    }

    // Create collection name for Qdrant (using user_id and collection_id)
    const qdrantCollectionName = `user_${user_id}_collection_${collection_id}`;

    try {
      // Check if collection exists, create if it doesn't
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        await qdrantClient.createCollection(qdrantCollectionName, {
          vectors: {
            size: vector.length,
            distance: 'Cosine',
          },
        });
      }

      // Create a unique point ID based on question content
      // Qdrant requires point IDs to be either unsigned integers or UUIDs
      // We'll create a deterministic UUID based on the collection_id and question
      const crypto = require('crypto');
      const hash = crypto.createHash('md5').update(`${collection_id}_${question}`).digest('hex');
      const pointId = `${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}`;

      // Validate point ID
      if (!pointId || pointId.length === 0) {
        return NextResponse.json(
          { error: 'Failed to generate valid point ID' },
          { status: 400 }
        );
      }

      console.log('📊 Preparing Qdrant upsert:', {
        pointId,
        vectorLength: vector.length,
        vectorType: typeof vector,
        isArray: Array.isArray(vector),
        firstFewValues: vector.slice(0, 3),
        collectionName: qdrantCollectionName
      });

      const upsertData = {
        wait: true,
        points: [
          {
            id: pointId,
            vector: vector,
            payload: {
              question,
              answer,
              collection_id,
              user_id,
              created_at: new Date().toISOString(),
            },
          },
        ],
      };

      console.log('📊 Upsert data structure (without vector):', JSON.stringify({
        ...upsertData,
        points: upsertData.points.map(point => ({
          ...point,
          vector: `[${point.vector.length} dimensions]`
        }))
      }, null, 2));

      // Upsert the vector point
      await qdrantClient.upsert(qdrantCollectionName, upsertData);

      return NextResponse.json({
        success: true,
        message: 'Vector synced to Qdrant successfully',
        pointId,
        collectionName: qdrantCollectionName,
      });

    } catch (qdrantError) {
      console.error('Qdrant operation failed:', qdrantError);
      return NextResponse.json(
        { error: 'Failed to sync with Qdrant', details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in qdrant-sync API:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
