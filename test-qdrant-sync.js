// Test script to reproduce Qdrant sync error
const testQdrantSync = async () => {
  try {
    console.log('Testing Qdrant sync...');
    
    // First, generate a test embedding vector (384 dimensions for all-MiniLM-L6-v2)
    const testVector = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
    
    const testData = {
      question: "What is the capital of France?",
      answer: "The capital of France is Paris.",
      vector: testVector,
      collection_id: "d74cf862-90ff-4b6f-9b61-bb8e08d2a43c",
      user_id: "112358367246483947704"
    };
    
    console.log('Sending request to qdrant-sync API...');
    console.log('Data:', {
      question_length: testData.question.length,
      answer_length: testData.answer.length,
      vector_length: testData.vector.length,
      collection_id: testData.collection_id,
      user_id: testData.user_id
    });
    
    const response = await fetch('http://localhost:3001/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-session-token-here' // You'll need to get this from browser
      },
      body: JSON.stringify(testData)
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const result = await response.json();
    console.log('Response body:', result);
    
    if (!response.ok) {
      console.error('❌ Request failed:', result);
    } else {
      console.log('✅ Request successful:', result);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Run the test
testQdrantSync();
