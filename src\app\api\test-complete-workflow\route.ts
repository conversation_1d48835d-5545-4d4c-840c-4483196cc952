import { NextRequest, NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing complete Q&A workflow...');

    // Check if required services are configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Qdrant is not configured',
          code: 'QDRANT_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Gemini API key is not configured',
          code: 'GEMINI_NOT_CONFIGURED'
        },
        { status: 503 }
      );
    }

    // Initialize clients
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.rag.gemini.model });

    // Test data
    const testCollectionId = 'test-workflow-' + Date.now();
    const testUserId = 'test-user-workflow';
    const testQA = {
      question: "What is the capital of France?",
      answer: "The capital of France is Paris, a beautiful city known for the Eiffel Tower and rich cultural heritage."
    };

    console.log('📊 Test data:', {
      collection_id: testCollectionId,
      user_id: testUserId,
      question: testQA.question,
      answer_length: testQA.answer.length
    });

    const results = {
      steps: [],
      embedding_generation: null,
      qdrant_operations: null,
      vector_search: null,
      rag_response: null,
      overall_success: false
    };

    try {
      // Step 1: Generate embedding directly (server-side)
      console.log('🔄 Step 1: Generating embedding...');

      // Import transformers library directly
      const { pipeline } = await import('@xenova/transformers');

      // Initialize embedding pipeline
      const embeddingPipeline = await pipeline(
        'feature-extraction',
        'Xenova/all-MiniLM-L6-v2',
        {
          device: 'cpu',
          dtype: 'fp32',
        }
      );

      // Combine question and answer
      const combinedText = `Question: ${testQA.question}\nAnswer: ${testQA.answer}`;

      // Generate embedding
      const result = await embeddingPipeline(combinedText, {
        pooling: 'mean',
        normalize: true,
      });

      // Extract the embedding vector
      const embedding = Array.from(result.data) as number[];

      const embeddingResult = {
        success: true,
        embedding: embedding,
        dimensions: embedding.length,
        method: 'server-side-direct',
        model: 'Xenova/all-MiniLM-L6-v2'
      };

      results.embedding_generation = {
        success: true,
        dimensions: embeddingResult.dimensions,
        method: embeddingResult.method,
        model: embeddingResult.model
      };
      results.steps.push('✅ Embedding generated successfully');
      console.log('✅ Step 1 completed: Embedding generated');

      // Step 2: Create Qdrant collection and upsert vector
      console.log('🔄 Step 2: Creating collection and upserting vector...');
      const qdrantCollectionName = `user_${testUserId}_collection_${testCollectionId}`;
      
      // Create collection
      await qdrantClient.createCollection(qdrantCollectionName, {
        vectors: {
          size: embeddingResult.dimensions,
          distance: 'Cosine',
        },
      });

      // Generate point ID
      const crypto = require('crypto');
      const hash = crypto.createHash('md5').update(`${testCollectionId}_${testQA.question}`).digest('hex');
      const pointId = `${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}`;

      // Upsert vector
      await qdrantClient.upsert(qdrantCollectionName, {
        wait: true,
        points: [
          {
            id: pointId,
            vector: embeddingResult.embedding,
            payload: {
              question: testQA.question,
              answer: testQA.answer,
              collection_id: testCollectionId,
              user_id: testUserId,
              created_at: new Date().toISOString(),
            },
          },
        ],
      });

      results.qdrant_operations = {
        success: true,
        collection_name: qdrantCollectionName,
        point_id: pointId,
        vector_dimensions: embeddingResult.dimensions
      };
      results.steps.push('✅ Vector upserted to Qdrant');
      console.log('✅ Step 2 completed: Vector upserted');

      // Step 3: Test vector search
      console.log('🔄 Step 3: Testing vector search...');
      const searchResults = await qdrantClient.search(qdrantCollectionName, {
        vector: embeddingResult.embedding,
        limit: 5,
        score_threshold: 0.5,
        with_payload: true,
      });

      results.vector_search = {
        success: true,
        results_count: searchResults.length,
        top_score: searchResults[0]?.score || 0,
        found_exact_match: searchResults.length > 0 && searchResults[0].score > 0.99
      };
      results.steps.push(`✅ Vector search completed (${searchResults.length} results)`);
      console.log('✅ Step 3 completed: Vector search');

      // Step 4: Test RAG response generation
      console.log('🔄 Step 4: Generating RAG response...');
      const context = searchResults.map(result => 
        `Q: ${result.payload?.question}\nA: ${result.payload?.answer}`
      ).join('\n\n');

      const testQuestion = "What is the capital of France?";
      const ragPrompt = `Based on the following context, answer the question: "${testQuestion}"

Context:
${context}

Please provide a helpful answer based on the context above. If the context doesn't contain relevant information, say so.`;

      const ragResult = await model.generateContent(ragPrompt);
      const ragResponse = ragResult.response;
      const ragAnswer = ragResponse.text();

      results.rag_response = {
        success: true,
        question: testQuestion,
        answer: ragAnswer,
        answer_length: ragAnswer.length,
        context_used: context.length > 0
      };
      results.steps.push('✅ RAG response generated');
      console.log('✅ Step 4 completed: RAG response generated');

      // Step 5: Cleanup
      console.log('🔄 Step 5: Cleaning up test data...');
      await qdrantClient.deleteCollection(qdrantCollectionName);
      results.steps.push('✅ Test data cleaned up');
      console.log('✅ Step 5 completed: Cleanup done');

      results.overall_success = true;

      return NextResponse.json({
        success: true,
        message: 'Complete Q&A workflow test passed successfully',
        results,
        workflow_steps: [
          'Generate embedding using server-side API',
          'Create Qdrant collection and upsert vector',
          'Test vector search functionality',
          'Generate RAG response using context',
          'Clean up test data'
        ],
        performance: {
          embedding_method: embeddingResult.method,
          vector_dimensions: embeddingResult.dimensions,
          search_results: searchResults.length,
          exact_match_found: results.vector_search.found_exact_match
        }
      });

    } catch (workflowError) {
      console.error('❌ Workflow step failed:', workflowError);
      
      // Try to clean up if collection was created
      try {
        const qdrantCollectionName = `user_${testUserId}_collection_${testCollectionId}`;
        await qdrantClient.deleteCollection(qdrantCollectionName);
        console.log('✅ Cleaned up test collection after error');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up test collection:', cleanupError);
      }

      return NextResponse.json(
        { 
          success: false,
          error: 'Workflow test failed',
          details: workflowError instanceof Error ? workflowError.message : 'Unknown error',
          completed_steps: results.steps,
          partial_results: results
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Complete workflow test failed:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Test setup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
