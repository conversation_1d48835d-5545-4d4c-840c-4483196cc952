'use client';

import { useState } from 'react';
import { 
  Card, 
  TextInput, 
  Button, 
  Select, 
  Stack, 
  Text, 
  Paper, 
  Loader,
  Alert,
  Badge,
  Group,
  Title,
  Divider
} from '@mantine/core';
import { IconSend, IconBrain, IconSearch, IconLanguage } from '@tabler/icons-react';
import { generateRAGResponse, searchSimilarQA, getAvailableLanguages, type RAGChatResponse, type RAGSearchResponse } from '@/lib/rag-client';
import { notifications } from '@mantine/notifications';

interface RAGChatInterfaceProps {
  collection_id: string;
  user_id: string;
}

export function RAGChatInterface({ collection_id, user_id }: RAGChatInterfaceProps) {
  const [question, setQuestion] = useState('');
  const [language, setLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(false);
  const [chatResponse, setChatResponse] = useState<RAGChatResponse | null>(null);
  const [searchResults, setSearchResults] = useState<RAGSearchResponse | null>(null);
  const [activeTab, setActiveTab] = useState<'chat' | 'search'>('chat');

  const availableLanguages = getAvailableLanguages();

  const handleChat = async () => {
    if (!question.trim()) {
      notifications.show({
        title: 'Error',
        message: 'Please enter a question',
        color: 'red',
      });
      return;
    }

    setIsLoading(true);
    setChatResponse(null);

    try {
      const response = await generateRAGResponse({
        question: question.trim(),
        collection_id,
        user_id,
        language,
      });

      setChatResponse(response);
      
      notifications.show({
        title: 'Success',
        message: 'RAG response generated successfully',
        color: 'green',
      });

    } catch (error) {
      console.error('❌ RAG chat error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to generate response';
      let errorTitle = 'Chat Error';

      if (error instanceof Error) {
        if (error.message.includes('embedding')) {
          errorTitle = 'Embedding Error';
          errorMessage = `Failed to generate search embedding: ${error.message}. Please check your internet connection and try again.`;
        } else if (error.message.includes('Qdrant') || error.message.includes('vector')) {
          errorTitle = 'Search Error';
          errorMessage = `Vector search failed: ${error.message}. Please ensure Qdrant is properly configured.`;
        } else if (error.message.includes('Gemini') || error.message.includes('AI')) {
          errorTitle = 'AI Response Error';
          errorMessage = `AI response generation failed: ${error.message}. Please try again.`;
        } else {
          errorMessage = error.message;
        }
      }

      notifications.show({
        title: errorTitle,
        message: errorMessage,
        color: 'red',
        autoClose: 8000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!question.trim()) {
      notifications.show({
        title: 'Error',
        message: 'Please enter a search query',
        color: 'red',
      });
      return;
    }

    setIsLoading(true);
    setSearchResults(null);

    try {
      const response = await searchSimilarQA({
        query: question.trim(),
        collection_id,
        user_id,
      });

      setSearchResults(response);
      
      notifications.show({
        title: 'Success',
        message: `Found ${response.total_results} similar Q&A pairs`,
        color: 'green',
      });

    } catch (error) {
      console.error('❌ RAG search error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to search';
      let errorTitle = 'Search Error';

      if (error instanceof Error) {
        if (error.message.includes('embedding')) {
          errorTitle = 'Embedding Error';
          errorMessage = `Failed to generate search embedding: ${error.message}. Please check your internet connection and try again.`;
        } else if (error.message.includes('Qdrant') || error.message.includes('vector')) {
          errorTitle = 'Vector Search Error';
          errorMessage = `Vector search failed: ${error.message}. Please ensure Qdrant is properly configured and has data.`;
        } else {
          errorMessage = error.message;
        }
      }

      notifications.show({
        title: errorTitle,
        message: errorMessage,
        color: 'red',
        autoClose: 8000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack gap="md">
        <Group justify="space-between">
          <Title order={3}>RAG Chat & Search</Title>
          <Group gap="xs">
            <Button
              variant={activeTab === 'chat' ? 'filled' : 'light'}
              size="xs"
              leftSection={<IconBrain size={16} />}
              onClick={() => setActiveTab('chat')}
            >
              Chat
            </Button>
            <Button
              variant={activeTab === 'search' ? 'filled' : 'light'}
              size="xs"
              leftSection={<IconSearch size={16} />}
              onClick={() => setActiveTab('search')}
            >
              Search
            </Button>
          </Group>
        </Group>

        <TextInput
          label={activeTab === 'chat' ? 'Ask a question' : 'Search query'}
          placeholder={activeTab === 'chat' ? 'What would you like to know?' : 'Search for similar Q&A pairs...'}
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              activeTab === 'chat' ? handleChat() : handleSearch();
            }
          }}
        />

        {activeTab === 'chat' && (
          <Select
            label="Language"
            placeholder="Select language"
            value={language}
            onChange={(value) => setLanguage(value || 'en')}
            data={availableLanguages.map(lang => ({
              value: lang.code,
              label: lang.name,
            }))}
            leftSection={<IconLanguage size={16} />}
          />
        )}

        <Button
          onClick={activeTab === 'chat' ? handleChat : handleSearch}
          loading={isLoading}
          leftSection={activeTab === 'chat' ? <IconBrain size={16} /> : <IconSearch size={16} />}
          disabled={!question.trim()}
        >
          {activeTab === 'chat' ? 'Generate Answer' : 'Search Similar Q&A'}
        </Button>

        {/* Chat Response */}
        {activeTab === 'chat' && chatResponse && (
          <Paper p="md" withBorder>
            <Stack gap="sm">
              <Group justify="space-between">
                <Text fw={500}>AI Response</Text>
                <Group gap="xs">
                  <Badge size="sm" color="blue">{chatResponse.language}</Badge>
                  <Badge size="sm" color="green">
                    {chatResponse.context.results_found} context results
                  </Badge>
                </Group>
              </Group>
              
              <Text>{chatResponse.answer}</Text>
              
              {chatResponse.context.search_results.length > 0 && (
                <>
                  <Divider />
                  <Text size="sm" fw={500}>Context Sources:</Text>
                  {chatResponse.context.search_results.map((result, index) => (
                    <Paper key={index} p="xs" bg="gray.0" radius="sm">
                      <Text size="xs" c="dimmed">
                        Relevance: {(result.score * 100).toFixed(1)}%
                      </Text>
                      <Text size="sm" fw={500}>{result.question}</Text>
                      <Text size="sm">{result.answer}</Text>
                    </Paper>
                  ))}
                </>
              )}
            </Stack>
          </Paper>
        )}

        {/* Search Results */}
        {activeTab === 'search' && searchResults && (
          <Paper p="md" withBorder>
            <Stack gap="sm">
              <Group justify="space-between">
                <Text fw={500}>Search Results</Text>
                <Badge size="sm" color="blue">
                  {searchResults.total_results} results
                </Badge>
              </Group>
              
              {searchResults.results.length === 0 ? (
                <Alert color="yellow">
                  No similar Q&A pairs found. Try a different search query.
                </Alert>
              ) : (
                searchResults.results.map((result, index) => (
                  <Paper key={result.id} p="sm" withBorder radius="sm">
                    <Stack gap="xs">
                      <Group justify="space-between">
                        <Text size="sm" fw={500}>Result {index + 1}</Text>
                        <Badge size="xs" color="green">
                          {(result.score * 100).toFixed(1)}% match
                        </Badge>
                      </Group>
                      <Text size="sm" fw={500}>{result.question}</Text>
                      <Text size="sm" c="dimmed">{result.answer}</Text>
                    </Stack>
                  </Paper>
                ))
              )}
            </Stack>
          </Paper>
        )}

        {isLoading && (
          <Paper p="md" withBorder>
            <Group>
              <Loader size="sm" />
              <Text size="sm">
                {activeTab === 'chat' ? 'Generating AI response...' : 'Searching for similar Q&A pairs...'}
              </Text>
            </Group>
          </Paper>
        )}
      </Stack>
    </Card>
  );
}
